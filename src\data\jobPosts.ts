import { JobPost } from '@/components/ui/JobCard';

export const jobPosts: JobPost[] = [
  {
    id: '1',
    title: 'Senior Frontend Developer',
    company: 'TechCorp',
    location: 'San Francisco, CA',
    salary: '$120,000 - $150,000',
    type: 'Full-time',
    description: 'We are looking for an experienced Frontend Developer to join our team. You will be responsible for building user interfaces for our web applications using React, TypeScript, and modern CSS frameworks.',
    postedDate: '2 days ago',
    logo: '/company-logos/techcorp.png',
    tags: ['React', 'TypeScript', 'Tailwind CSS', 'Next.js']
  },
  {
    id: '2',
    title: 'UI/UX Designer',
    company: 'Design Studio',
    location: 'Remote',
    salary: '$90,000 - $110,000',
    type: 'Full-time',
    description: 'Design Studio is seeking a talented UI/UX Designer to create beautiful and functional user interfaces for our clients. You will work closely with our development team to implement your designs.',
    postedDate: '3 days ago',
    logo: '/company-logos/designstudio.png',
    tags: ['Figma', 'Adobe XD', 'UI Design', 'UX Research']
  },
  {
    id: '3',
    title: 'Full Stack Developer',
    company: 'Web Innovations',
    location: 'New York, NY',
    salary: '$130,000 - $160,000',
    type: 'Full-time',
    description: 'Join our team as a Full Stack Developer and work on exciting projects using the latest technologies. You will be responsible for both frontend and backend development of our web applications.',
    postedDate: '1 week ago',
    logo: '/company-logos/webinnovations.png',
    tags: ['JavaScript', 'Node.js', 'React', 'MongoDB']
  },
  {
    id: '4',
    title: 'React Native Developer',
    company: 'Mobile Apps Inc.',
    location: 'Chicago, IL',
    salary: '$100,000 - $130,000',
    type: 'Full-time',
    description: 'We are looking for a React Native Developer to build mobile applications for iOS and Android. You will work with our design and backend teams to create seamless mobile experiences.',
    postedDate: '5 days ago',
    logo: '/company-logos/mobileapps.png',
    tags: ['React Native', 'JavaScript', 'iOS', 'Android']
  },
  {
    id: '5',
    title: 'Backend Engineer',
    company: 'Data Systems',
    location: 'Seattle, WA',
    salary: '$140,000 - $170,000',
    type: 'Full-time',
    description: 'Join our backend team to build scalable and efficient server-side applications. You will work with databases, APIs, and cloud infrastructure to support our growing platform.',
    postedDate: '2 weeks ago',
    logo: '/company-logos/datasystems.png',
    tags: ['Python', 'Django', 'PostgreSQL', 'AWS']
  },
  {
    id: '6',
    title: 'DevOps Engineer',
    company: 'Cloud Solutions',
    location: 'Austin, TX',
    salary: '$120,000 - $150,000',
    type: 'Full-time',
    description: 'We are seeking a DevOps Engineer to help us automate and optimize our infrastructure. You will work with cloud platforms, CI/CD pipelines, and monitoring tools to ensure our systems run smoothly.',
    postedDate: '3 days ago',
    logo: '/company-logos/cloudsolutions.png',
    tags: ['AWS', 'Docker', 'Kubernetes', 'CI/CD']
  },
  {
    id: '7',
    title: 'Product Manager',
    company: 'Innovative Products',
    location: 'Boston, MA',
    salary: '$110,000 - $140,000',
    type: 'Full-time',
    description: 'We are looking for a Product Manager to lead the development of our digital products. You will work with cross-functional teams to define product vision, roadmap, and features.',
    postedDate: '1 week ago',
    logo: '/company-logos/innovativeproducts.png',
    tags: ['Product Management', 'Agile', 'User Research', 'Roadmapping']
  },
  {
    id: '8',
    title: 'Data Scientist',
    company: 'Analytics Co.',
    location: 'Remote',
    salary: '$130,000 - $160,000',
    type: 'Full-time',
    description: 'Join our data science team to analyze complex datasets and build machine learning models. You will help us extract insights from data and develop algorithms to solve business problems.',
    postedDate: '4 days ago',
    logo: '/company-logos/analyticsco.png',
    tags: ['Python', 'Machine Learning', 'SQL', 'Data Analysis']
  }
];
