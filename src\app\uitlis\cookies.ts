/**
 * Set a cookie in the browser
 * @param name - The name of the cookie
 * @param value - The value of the cookie
 * @param days - The number of days until the cookie expires
 */
export function setCookie(name: string, value: string, days: number = 1): void {
  if (typeof window === 'undefined') return;
  
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${name}=${value};${expires};path=/`;
}

/**
 * Get a cookie value by name
 * @param name - The name of the cookie
 * @returns The value of the cookie or an empty string if not found
 */
export function getCookie(name: string): string {
  if (typeof window === 'undefined') return '';
  
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  
  return '';
}

/**
 * Delete a cookie by name
 * @param name - The name of the cookie to delete
 */
export function deleteCookie(name: string): void {
  if (typeof window === 'undefined') return;
  
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
}
