{"name": "job-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/jsonwebtoken": "^9.0.9", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "next": "15.3.1", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}